# .NET 10 BorderlessEntry Material Components Fix

## Problem
After upgrading to .NET 10, the BorderlessEntry control throws a `Java.Lang.IllegalArgumentException` with the message:
"This component requires that you specify a valid TextAppearance attribute. Update your app theme to inherit from Theme.MaterialComponents (or a descendant)."

## Root Cause
.NET 10 MAUI has stricter requirements for Material Components compliance. The previous approach of setting `platformView.Background = null` conflicts with Material Components' internal styling requirements.

## Solution Overview
The fix involves three main changes:

1. **Add Required TextAppearance Attributes**: Update Android themes to include all required Material Components TextAppearance attributes
2. **Use Material Components-Compatible Background**: Replace `null` background with transparent drawable
3. **Maintain Proper Text Appearance**: Ensure Material Components text styling is preserved

## Implementation Details

### 1. Updated Android Themes
Added required TextAppearance attributes to both light and dark themes:
- `textAppearanceHeadline1` through `textAppearanceHeadline6`
- `textAppearanceSubtitle1`, `textAppearanceSubtitle2`
- `textAppearanceBody1`, `textAppearanceBody2`
- `textAppearanceButton`, `textAppearanceCaption`, `textAppearanceOverline`

### 2. Transparent Drawable Resource
Created `transparent_background.xml` drawable resource to replace null background.

### 3. Updated Handlers
Modified `BorderlessEntryHandler` and `BorderlessEditorHandler` to:
- Use transparent drawable instead of null background
- Set proper TextAppearance for Material Components
- Include fallback error handling
- Maintain cross-platform compatibility

## Files Modified
- `src/Briefly.MobileApp/Platforms/Android/Resources/values/styles.xml`
- `src/Briefly.MobileApp/Platforms/Android/Resources/values-night/styles.xml`
- `src/Platform.Client.Common/Controls/BorderlessEditor.cs`

## Files Added
- `src/Briefly.MobileApp/Platforms/Android/Resources/drawable/transparent_background.xml`
- `src/Platform.Client.Common/Controls/BorderlessEntryAlternative.cs` (alternative implementation)

## Testing
1. Build and run the application on Android
2. Test BorderlessEntry controls in both light and dark themes
3. Verify no IllegalArgumentException is thrown
4. Confirm borderless appearance is maintained

## Alternative Implementation
If the main solution doesn't work, use `BorderlessEntryAlternative` which creates transparent drawables programmatically.

## Cross-Platform Compatibility
The solution maintains compatibility with:
- ✅ Android (API 21+)
- ✅ iOS
- ✅ Windows

## Notes
- The fix is backward compatible with earlier .NET versions
- Error handling ensures graceful fallback if resources are unavailable
- Material Components compliance is maintained while preserving borderless appearance
