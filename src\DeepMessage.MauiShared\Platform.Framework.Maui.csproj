﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net10.0-android;net10.0-ios</TargetFrameworks>
 
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion> 
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Acr.UserDialogs" Version="9.2.2" />
		<PackageReference Include="CommunityToolkit.Maui" Version="12.2.0" />
		
		<PackageReference Include="Microsoft.Maui.Controls" Version="10.0.0-preview.7.25406.3" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="10.0.0-preview.7.25406.3" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="10.0.0-preview.7.25380.108" />
		<PackageReference Include="SkiaSharp" Version="3.119.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.4-beta1" />
		<!--<PackageReference Include="Plugin.Firebase.Crashlytics" Version="3.1.1" />
		<PackageReference Include="Plugin.Firebase.CloudMessaging" Version="3.1.2" />-->
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\DeepMessage.Framework.Core\Platform.Framework.Core.csproj" />
	</ItemGroup>

</Project>
