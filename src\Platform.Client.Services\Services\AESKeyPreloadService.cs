using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using DeepMessage.MauiApp.Services;
using System.Collections.Concurrent;
using Platform.Framework.Core;

namespace Platform.Client.Services.Services;

/// <summary>
/// Interface for AES key preloading service
/// Handles preemptive loading and batch operations for optimal performance
/// </summary>
public interface IAESKeyPreloadService
{
    /// <summary>
    /// Preloads AES keys for active conversations
    /// </summary>
    /// <param name="conversationIds">List of conversation IDs to preload keys for</param>
    Task PreloadKeysForConversationsAsync(IEnumerable<string> conversationIds);

    /// <summary>
    /// Preloads AES keys for all active friendships
    /// </summary>
    Task PreloadAllActiveFriendshipKeysAsync();

    /// <summary>
    /// Batch loads AES keys for multiple friendships
    /// </summary>
    /// <param name="friendshipIds">List of friendship IDs</param>
    Task BatchLoadAESKeysAsync(IEnumerable<string> friendshipIds);

    /// <summary>
    /// Starts background key preloading for the current user
    /// </summary>
    Task StartBackgroundPreloadingAsync();

    /// <summary>
    /// Stops background key preloading
    /// </summary>
    Task StopBackgroundPreloadingAsync();

    /// <summary>
    /// Gets preloading statistics
    /// </summary>
    PreloadStatistics GetPreloadStatistics();
}

/// <summary>
/// Preloading statistics for monitoring and debugging
/// </summary>
public class PreloadStatistics
{
    public int TotalKeysPreloaded { get; set; }
    public int SuccessfulPreloads { get; set; }
    public int FailedPreloads { get; set; }
    public DateTime LastPreloadTime { get; set; }
    public TimeSpan AveragePreloadTime { get; set; }
}

/// <summary>
/// AES key preloading service implementation
/// Optimizes chat performance by preemptively loading encryption keys
/// </summary>
public class AESKeyPreloadService : IAESKeyPreloadService, IDisposable
{
    private readonly IFriendsListingDataService _friendsListingService;
    private readonly IAESKeyManagerService _aesKeyManagerService;
    private readonly ILocalStorageService _localStorageService;
    private readonly ILogger<AESKeyPreloadService> _logger;

    private readonly ConcurrentDictionary<string, DateTime> _preloadedKeys = new();
    private readonly SemaphoreSlim _preloadSemaphore = new(1, 1);
    private Timer? _backgroundTimer;
    private bool _isBackgroundPreloadingActive = false;
    private bool _disposed = false;

    // Statistics
    private int _totalKeysPreloaded = 0;
    private int _successfulPreloads = 0;
    private int _failedPreloads = 0;
    private DateTime _lastPreloadTime = DateTime.MinValue;
    private readonly List<TimeSpan> _preloadTimes = new();

    // Configuration
    private const int BACKGROUND_PRELOAD_INTERVAL_MINUTES = 30;
    private const int MAX_CONCURRENT_PRELOADS = 5;
    private const int PRELOAD_CACHE_DURATION_HOURS = 1;

    public AESKeyPreloadService(
        IFriendsListingDataService friendsListingService,
        IAESKeyManagerService aesKeyManagerService,
        ILocalStorageService localStorageService,
        ILogger<AESKeyPreloadService> logger)
    {
        _friendsListingService = friendsListingService;
        _aesKeyManagerService = aesKeyManagerService;
        _localStorageService = localStorageService;
        _logger = logger;
    }

    public async Task PreloadKeysForConversationsAsync(IEnumerable<string> conversationIds)
    {
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("Cannot preload keys: user not authenticated");
                return;
            }

            // Get all friendships to map conversation participants to friendship IDs
            var friendsFilter = new FriendsFilterBusinessObject { UsePagination = false };
            var friendsResult = await _friendsListingService.GetPaginatedItems(friendsFilter);
            
            if (friendsResult.Items == null || !friendsResult.Items.Any())
            {
                _logger.LogDebug("No friendships found for key preloading");
                return;
            }

            // For each conversation, preload keys for the friendship
            var preloadTasks = new List<Task>();
            var semaphore = new SemaphoreSlim(MAX_CONCURRENT_PRELOADS, MAX_CONCURRENT_PRELOADS);

            foreach (var conversationId in conversationIds)
            {
                // In a real implementation, you'd need to map conversation IDs to friendship IDs
                // For now, we'll preload keys for all active friendships
                foreach (var friend in friendsResult.Items)
                {
                    preloadTasks.Add(PreloadKeyWithSemaphore(semaphore, friend.Id, userId, friend.FriendId));
                }
            }

            await Task.WhenAll(preloadTasks);
            _logger.LogInformation("Completed preloading keys for {Count} conversations", conversationIds.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error preloading keys for conversations");
        }
    }

    public async Task PreloadAllActiveFriendshipKeysAsync()
    {
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("Cannot preload keys: user not authenticated");
                return;
            }

            var friendsFilter = new FriendsFilterBusinessObject { UsePagination = false };
            var friendsResult = await _friendsListingService.GetPaginatedItems(friendsFilter);
            
            if (friendsResult.Items == null || !friendsResult.Items.Any())
            {
                _logger.LogDebug("No friendships found for key preloading");
                return;
            }

            var friendshipIds = friendsResult.Items.Select(f => f.Id);
            await BatchLoadAESKeysAsync(friendshipIds);

            _logger.LogInformation("Preloaded keys for {Count} active friendships", friendsResult.Items.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error preloading all friendship keys");
        }
    }

    public async Task BatchLoadAESKeysAsync(IEnumerable<string> friendshipIds)
    {
        await _preloadSemaphore.WaitAsync();
        try
        {
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return;
            }

            var preloadTasks = new List<Task>();
            var semaphore = new SemaphoreSlim(MAX_CONCURRENT_PRELOADS, MAX_CONCURRENT_PRELOADS);

            foreach (var friendshipId in friendshipIds)
            {
                // Skip if recently preloaded
                if (_preloadedKeys.TryGetValue(friendshipId, out var lastPreload) &&
                    DateTime.UtcNow - lastPreload < TimeSpan.FromHours(PRELOAD_CACHE_DURATION_HOURS))
                {
                    continue;
                }

                // Get friend ID from friendship ID (simplified approach)
                var friendId = await GetFriendIdFromFriendshipAsync(friendshipId);
                if (!string.IsNullOrEmpty(friendId))
                {
                    preloadTasks.Add(PreloadKeyWithSemaphore(semaphore, friendshipId, userId, friendId));
                }
            }

            await Task.WhenAll(preloadTasks);
            _lastPreloadTime = DateTime.UtcNow;
        }
        finally
        {
            _preloadSemaphore.Release();
        }
    }

    public async Task StartBackgroundPreloadingAsync()
    {
        if (_isBackgroundPreloadingActive)
        {
            return;
        }

        _isBackgroundPreloadingActive = true;
        _backgroundTimer = new Timer(BackgroundPreloadCallback, null, 
            TimeSpan.Zero, 
            TimeSpan.FromMinutes(BACKGROUND_PRELOAD_INTERVAL_MINUTES));

        _logger.LogInformation("Started background AES key preloading");
        await Task.CompletedTask;
    }

    public async Task StopBackgroundPreloadingAsync()
    {
        _isBackgroundPreloadingActive = false;
        _backgroundTimer?.Dispose();
        _backgroundTimer = null;

        _logger.LogInformation("Stopped background AES key preloading");
        await Task.CompletedTask;
    }

    public PreloadStatistics GetPreloadStatistics()
    {
        var averageTime = _preloadTimes.Count > 0 
            ? TimeSpan.FromMilliseconds(_preloadTimes.Average(t => t.TotalMilliseconds))
            : TimeSpan.Zero;

        return new PreloadStatistics
        {
            TotalKeysPreloaded = _totalKeysPreloaded,
            SuccessfulPreloads = _successfulPreloads,
            FailedPreloads = _failedPreloads,
            LastPreloadTime = _lastPreloadTime,
            AveragePreloadTime = averageTime
        };
    }

    private async Task PreloadKeyWithSemaphore(SemaphoreSlim semaphore, string friendshipId, string userId, string friendId)
    {
        await semaphore.WaitAsync();
        try
        {
            var startTime = DateTime.UtcNow;
            
            // Attempt to preload the AES key
            await _aesKeyManagerService.GetOrCreateAESKeyAsync(friendshipId, userId, friendId);
            
            var preloadTime = DateTime.UtcNow - startTime;
            _preloadTimes.Add(preloadTime);
            
            _preloadedKeys.AddOrUpdate(friendshipId, DateTime.UtcNow, (key, oldValue) => DateTime.UtcNow);
            
            Interlocked.Increment(ref _totalKeysPreloaded);
            Interlocked.Increment(ref _successfulPreloads);
            
            _logger.LogDebug("Preloaded AES key for friendship {FriendshipId} in {Duration}ms", 
                friendshipId, preloadTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _failedPreloads);
            _logger.LogWarning(ex, "Failed to preload AES key for friendship {FriendshipId}", friendshipId);
        }
        finally
        {
            semaphore.Release();
        }
    }

    private async Task<string?> GetFriendIdFromFriendshipAsync(string friendshipId)
    {
        try
        {
            // This is a simplified approach - in a real implementation,
            // you might need to query the friendship record to get the friend ID
            // For now, we'll use the friends listing service
            var friendsFilter = new FriendsFilterBusinessObject { UsePagination = false };
            var friendsResult = await _friendsListingService.GetPaginatedItems(friendsFilter);
            
            var friend = friendsResult.Items?.FirstOrDefault(f => f.Id == friendshipId);
            return friend?.FriendId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting friend ID for friendship {FriendshipId}", friendshipId);
            return null;
        }
    }

    private async void BackgroundPreloadCallback(object? state)
    {
        if (!_isBackgroundPreloadingActive)
        {
            return;
        }

        try
        {
            await PreloadAllActiveFriendshipKeysAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during background key preloading");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _backgroundTimer?.Dispose();
            _preloadSemaphore?.Dispose();
            _disposed = true;
        }
    }
}
