﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatThreadsServerSideListingDataService : ServerSideListingDataService<ChatThreadsListingBusinessObject, ChatThreadsFilterBusinessObject>, IChatThreadsListingDataService
{
    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatThreadsServerSideListingDataService(AppDbContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public override IQueryable<ChatThreadsListingBusinessObject> GetQuery(ChatThreadsFilterBusinessObject filterBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        var conversations = (from c in _context.Conversations
                             from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                             where p.UserId == userId
                             select c.Id).ToList();

        var query = (from c in _context.Conversations
                     from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                     from f in _context.Friendships.Where(x => x.FriendId == p.UserId)
                     where conversations.Contains(c.Id) && p.UserId != userId
                     select new ChatThreadsListingBusinessObject
                     {
                         Id = c.Id,
                         Avatar = f.AvatarData,
                         Name = f.Name,
                         LastMessage = (from m in _context.Messages
                                        from r in _context.MessageRecipients.Where(r => r.MessageId == m.Id && r.RecipientId == userId)
                                        where m.ConversationId == c.Id
                                        orderby m.CreatedAt descending
                                        select new ChatMessagesListingBusinessObject()
                                        {
                                            Content = r.EncryptedContent,
                                            DeliveryStatus = m.DeliveryStatus,
                                            SenderId = m.SenderId,
                                            Id = m.Id,
                                            Timestamp = m.CreatedAt,
                                            IsIncoming = m.SenderId != userId,
                                            ReceiverId = r.RecipientId
                                        }).FirstOrDefault()
                     });

        return query;
    }

    protected override PagedDataList<ChatThreadsListingBusinessObject> OnItemsLoaded(PagedDataList<ChatThreadsListingBusinessObject> items)
    {
        ArgumentNullException.ThrowIfNull(items?.Items);

        items.Items = items.Items.DistinctBy(x => x.Id).ToList();
        return items;
    }
}
