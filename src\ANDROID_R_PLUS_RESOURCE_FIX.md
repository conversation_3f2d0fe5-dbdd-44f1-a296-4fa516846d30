# Android R+ Resource Compression Fix

## Problem
When deploying to Android devices running API 30+ (Android 11+), the following error occurs:

```
ADB0010: Mono.AndroidTools.InstallFailedException: Unexpected install output: 
Failure [-124: Failed parse during installPackageLI: Targeting R+ (version 30 and above) 
requires the resources.arsc of installed APKs to be stored uncompressed and aligned on a 4-byte boundary]
```

## Root Cause
Starting with Android 11 (API 30), Google introduced stricter requirements for APK resource handling:
- The `resources.arsc` file must be stored **uncompressed**
- The file must be **4-byte aligned**
- This is enforced for all apps targeting API 30 or higher

## Solution Overview
The fix involves configuring the Android build system to:
1. Store `resources.arsc` uncompressed
2. Use AAPT2 with proper configuration
3. Set correct target SDK and manifest settings
4. Configure build properties for .NET 10 compatibility

## Implementation Details

### 1. Project File Updates (`ModelFury.Briefly.MobileApp.csproj`)

#### Added Android Configuration Properties:
```xml
<!-- Fix for Android R+ (API 30+) resource compression requirements -->
<AndroidStoreUncompressedFileExtensions>.arsc</AndroidStoreUncompressedFileExtensions>
<AndroidUseAapt2>true</AndroidUseAapt2>
<TargetSdkVersion>35</TargetSdkVersion>
```

#### Updated Build Configurations:
- Fixed target framework references from `net9.0-android` to `net10.0-android`
- Added Android-specific build settings for both Debug and Release
- Configured proper AOT and linking settings

### 2. AndroidManifest.xml Updates
- Added explicit `uses-sdk` declaration with `targetSdkVersion="35"`
- Added `android:extractNativeLibs="true"` for compatibility

### 3. AAPT2 Configuration (`aapt2.conf`)
Created configuration file to explicitly specify uncompressed file types:
```
-0 .arsc
-0 .so
-0 .dll
```

## Files Modified
- `src/Briefly.MobileApp/ModelFury.Briefly.MobileApp.csproj`
- `src/Briefly.MobileApp/Platforms/Android/AndroidManifest.xml`

## Files Added
- `src/Briefly.MobileApp/Platforms/Android/aapt2.conf`

## Key Configuration Properties

### AndroidStoreUncompressedFileExtensions
Forces specific file extensions to be stored uncompressed in the APK.

### AndroidUseAapt2
Ensures the modern Android Asset Packaging Tool (AAPT2) is used, which properly handles the 4-byte alignment requirement.

### TargetSdkVersion
Explicitly sets the target SDK to API 35, ensuring compatibility with latest Android requirements.

## Testing Steps
1. Clean the solution: `dotnet clean`
2. Rebuild the project: `dotnet build`
3. Deploy to Android device/emulator
4. Verify installation succeeds without the -124 error

## Troubleshooting

### If the error persists:
1. **Clean bin/obj folders**: Delete all build artifacts
2. **Check Android SDK**: Ensure you have the latest Android SDK tools
3. **Verify AAPT2**: Confirm AAPT2 is available in your Android SDK
4. **Check device API level**: Ensure test device is running API 21+ (minimum supported)

### Alternative Solutions:
If the main fix doesn't work, you can try:
1. **Lower target SDK**: Temporarily set `TargetSdkVersion` to 29 (not recommended for production)
2. **Use AAB format**: Switch to Android App Bundle format instead of APK
3. **Manual AAPT2 flags**: Add custom AAPT2 arguments in the project file

## Production Considerations
- This fix is required for Google Play Store submissions targeting API 30+
- The configuration maintains backward compatibility with older Android versions
- Performance impact is minimal as only specific files are stored uncompressed

## References
- [Android R+ APK Requirements](https://developer.android.com/about/versions/11/behavior-changes-11#resource-compression)
- [AAPT2 Documentation](https://developer.android.com/studio/command-line/aapt2)
- [.NET MAUI Android Configuration](https://docs.microsoft.com/en-us/dotnet/maui/android/)
