﻿<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
	<!-- Base Application Theme - Light Mode -->
	<style name="MainTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
		<!-- Primary App Colors -->
		<item name="android:colorPrimary">#444444</item>
		<item name="android:colorPrimaryDark">#222222</item>
		<item name="android:colorAccent">#333333</item>

		<!-- Enable theme-aware dark mode support -->
		<item name="android:forceDarkAllowed">true</item>

		<!-- Light Theme Status Bar Configuration -->
		<item name="android:statusBarColor">@color/status_bar_light</item>
		<item name="android:windowLightStatusBar">true</item>

		<!-- Light Theme Navigation Bar Configuration -->
		<item name="android:navigationBarColor">@color/navigation_bar_light</item>
		<item name="android:windowLightNavigationBar">true</item>

		<!-- Window Configuration -->
		<item name="android:windowDrawsSystemBarBackgrounds">true</item>
		<item name="android:fitsSystemWindows">false</item>

		<!-- Required TextAppearance attributes for Material Components -->
		<item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
		<item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
		<item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
		<item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
		<item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
		<item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
		<item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
		<item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
		<item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
		<item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
		<item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
		<item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
		<item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

	</style>

	<!-- Custom style for borderless EditText that works with Material Components -->
	<style name="BorderlessEditTextStyle" parent="Widget.MaterialComponents.TextInputEditText.FilledBox">
		<item name="android:background">@android:color/transparent</item>
		<item name="android:textAppearance">@style/TextAppearance.MaterialComponents.Body1</item>
	</style>
</resources>
