﻿using Android.Views;
using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.MauiApp.Services; 
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Platform;
using MobileApp.MauiShared;
using Platform.Framework.Core;

namespace ModelFury.Briefly.MobileApp
{
    public partial class App : Application
    {
        private readonly IServiceScopeFactory serviceScopeFactory;
        private readonly ILogger<App> logger;

        public App(IServiceScopeFactory serviceScopeFactory, ILogger<App> logger)
        {
            InitializeComponent();
#if ANDROID
            //DependencyService.Register<DeepMessage.MauiApp.Platforms.Android.ForegroundServiceManager>();
#endif

            this.serviceScopeFactory = serviceScopeFactory;
            this.logger = logger;
//            Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping("MyCustomization", (handler, view) =>
//            {
//#if ANDROID
//                handler.PlatformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(Colors.Transparent.ToAndroid());
//#endif
//            });

//            Microsoft.Maui.Handlers.EditorHandler.Mapper.AppendToMapping("MyCustomization", (handler, view) =>
//            {
//#if ANDROID
//                handler.PlatformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(Colors.Transparent.ToAndroid());
//#endif
           
//            });

        }
    

        protected override Microsoft.Maui.Controls.Window CreateWindow(IActivationState? activationState)
        {
            return new Microsoft.Maui.Controls.Window(new AppShell(serviceScopeFactory));
        }

        protected override void OnStart()
        {
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
             logger.LogError("Unhandled exception: {Exception}", args.ExceptionObject);
            };

            WeakReferenceMessenger.Default.Register<string>(this, async (r, m) =>
            {
                if (m == "Logout")
                {
                    await Shell.Current.GoToAsync("//news");
                }
            });

            // ✅ PERFORMANCE OPTIMIZATION: Defer heavy service startup to background
            // This prevents blocking the UI thread during app startup
            _ = Task.Run(async () =>
            {
                try
                {
                    // Add small delay to ensure app UI is fully loaded first
                    await Task.Delay(1000);
                    await StartSyncingAsync();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to start background synchronization services");
                }
            });

            base.OnStart();
        }

        protected override void OnResume()
        {
            // ✅ PERFORMANCE OPTIMIZATION: Use async version for OnResume as well
            _ = Task.Run(async () =>
            {
                try
                {
                    await StartSyncingAsync();
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to start synchronization services on resume");
                }
            });
            base.OnResume();
        }

        /// <summary>
        /// Starts synchronization services with proper resource management (Async version)
        /// ✅ PERFORMANCE OPTIMIZED: Now runs asynchronously to avoid blocking UI thread
        /// </summary>
        private async Task StartSyncingAsync()
        {
            try
            {
                logger.LogDebug("Starting synchronization services asynchronously...");

                // Run service startup in background thread
                await Task.Run(() =>
                {
                    using var scope = serviceScopeFactory.CreateScope();
                    scope.ServiceProvider.GetRequiredService<ChatSyncUpService>().Start();
                    scope.ServiceProvider.GetRequiredService<SignalRClientService>().Start(MauiProgram.ChatHubUrl!);
                });

                //DependencyService.Get<IForegroundServiceManager>()?.StartService();
                logger.LogDebug("Synchronization services started successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to start synchronization services");
            }
        }

        /// <summary>
        /// Legacy synchronous method - kept for compatibility but now calls async version
        /// </summary>
        private void StartSyncing()
        {
            _ = Task.Run(async () => await StartSyncingAsync());
        }

        /// <summary>
        /// Handles app sleep state with proper resource management
        /// ✅ FIXED: Proper service scope disposal and graceful shutdown
        /// </summary>
        protected override void OnSleep()
        {
            try
            {
                using var scope = serviceScopeFactory.CreateScope(); // ✅ FIXED: Proper disposal
                scope.ServiceProvider.GetRequiredService<ChatSyncUpService>().Stop();

                // ✅ FIXED: Graceful SignalR disconnection
                var signalRService = scope.ServiceProvider.GetRequiredService<SignalRClientService>();
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await signalRService.DisposeAsync();
                        logger.LogDebug("SignalR service disposed gracefully on sleep");
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Error disposing SignalR service on sleep");
                    }
                });

                logger.LogDebug("App entering sleep mode - services stopped");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error during app sleep transition");
            }
            base.OnSleep();
        }
    }

}