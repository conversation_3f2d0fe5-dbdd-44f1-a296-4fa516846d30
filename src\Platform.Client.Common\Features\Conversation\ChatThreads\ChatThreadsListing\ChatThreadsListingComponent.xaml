﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ChatThreadsListingViewBase
    x:Class="Platform.Client.Common.Features.Conversation.ChatThreadsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:chat="clr-namespace:Platform.Client.Services.Features.Conversation;assembly=Platform.Client.Services"
    xmlns:controls="clr-namespace:Platform.Client.Common.Controls"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Conversation"
    Title="Chats"
    x:DataType="local:ChatThreadsListingView"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray700}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray700}}"
    Shell.TitleColor="{AppThemeBinding Light={StaticResource Gray800},
                                       Dark={StaticResource White}}">

    <ContentPage.ToolbarItems>
        <ToolbarItem Command="{Binding SyncDownItemsCommand}">
            <ToolbarItem.IconImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf021;"
                    Size="16"
                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            </ToolbarItem.IconImageSource>
        </ToolbarItem>
        <ToolbarItem>
            <ToolbarItem.IconImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf0c9;"
                    Size="16"
                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            </ToolbarItem.IconImageSource>
        </ToolbarItem>
    </ContentPage.ToolbarItems>

    <Grid RowDefinitions="Auto,8, *" VerticalOptions="Fill">

        <Border
            Margin="8,0,8,0"
            Background="{AppThemeBinding Light={StaticResource Gray50},
                                         Dark={StaticResource Gray700}}"
            Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                     Dark={StaticResource Gray500}}">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="45" />
            </Border.StrokeShape>
            <Grid>
                <Image
                    Margin="16,0"
                    HorizontalOptions="End"
                    MaximumHeightRequest="32">
                    <Image.Source>
                        <FontImageSource
                            FontFamily="Jelly"
                            Glyph="&#xf002;"
                            Size="20"
                            Color="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                    </Image.Source>
                </Image>
                <controls:BorderlessEntry
                    Grid.Column="0"
                    Margin="12,0"
                    FontSize="16"
                    HorizontalOptions="Fill"
                    Placeholder="Search messages..." />


            </Grid>
        </Border>


        <CollectionView
            Grid.Row="2"
            BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                              Dark={StaticResource Gray700}}"
            ItemsSource="{Binding Items, Mode=OneWay}"
            ItemsUpdatingScrollMode="KeepLastItemInView"
            VerticalOptions="Fill">
            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="chat:ChatThreadsListingViewModel">
                    <Grid Padding="16,12" ColumnDefinitions="50,*">
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:ChatThreadsListingView}}, Path=BindingContext.MessageTappedCommand}" CommandParameter="{Binding .}" />
                        </Grid.GestureRecognizers>
                        <!--  Avatar  -->
                        <Grid HeightRequest="48" WidthRequest="48">
                            <Border
                                BackgroundColor="{StaticResource Gray200}"
                                HeightRequest="48"
                                Stroke="{StaticResource Gray400}"
                                WidthRequest="48">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="24" />
                                </Border.StrokeShape>
                                <Grid>
                                    <Image
                                        Aspect="AspectFill"
                                        IsVisible="{Binding Avatar, Converter={StaticResource StringToBoolConverter}}"
                                        Source="{Binding Avatar}" />
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="18"
                                        HorizontalOptions="Center"
                                        IsVisible="{Binding Avatar, Converter={StaticResource InverseStringToBoolConverter}}"
                                        Text="{Binding Name, Converter={StaticResource InitialsConverter}}"
                                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                    Dark={StaticResource Gray200}}"
                                        VerticalOptions="Center" />
                                </Grid>
                            </Border>
                        </Grid>
                        <StackLayout
                            Grid.Column="1"
                            Margin="12,0,8,0"
                            Spacing="2"
                            VerticalOptions="Center">
                            <Grid>
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="16"
                                    Text="{Binding Name}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                                Dark={StaticResource Gray100}}" />

                                <Label
                                    FontSize="12"
                                    HorizontalOptions="End"
                                    Text="{Binding LastMessageTimeString}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray200}}" />

                            </Grid>
                            <HorizontalStackLayout>
                                <Image
                                    Margin="4,0"
                                    HeightRequest="12"
                                    IsVisible="{Binding DeliverySymbol, Converter={StaticResource StringToBoolConverter}}"
                                    Source="{Binding DeliverySymbol}" />
                                <Label
                                    FontSize="15"
                                    LineBreakMode="TailTruncation"
                                    MaxLines="1"
                                    Text="{Binding LastMessage}"
                                    TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray200}}" />
                            </HorizontalStackLayout>
                        </StackLayout>

                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>

        <Border
            Grid.Row="2"
            Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                     Dark={StaticResource Gray800}}"
            StrokeThickness="0.5"
            VerticalOptions="End" />
    </Grid>

</local:ChatThreadsListingViewBase>
