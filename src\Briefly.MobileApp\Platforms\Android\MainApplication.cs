﻿using Android.App;
using Android.Content;
using Android.OS;
using Android.Runtime;
using Firebase;
using Plugin.Firebase.CloudMessaging;

namespace ModelFury.Briefly.MobileApp
{
    [Application]
    public class MainApplication : MauiApplication
    {
        //UpdateBroadcastReceiver? _receiver;
        public MainApplication(IntPtr handle, JniHandleOwnership ownership)
            : base(handle, ownership)
        {
            try
            {
                // ✅ ANDROID 9 COMPATIBILITY: Safe Firebase initialization with error handling
                System.Diagnostics.Debug.WriteLine($"Initializing MainApplication on Android {Build.VERSION.Release} (API {(int)Build.VERSION.SdkInt})");

                var options = FirebaseOptions.FromResource(this);
                if (options != null)
                {
                    var firebaseApp = FirebaseApp.InitializeApp(this, options);
                    if (firebaseApp == null)
                    {
                        System.Diagnostics.Debug.WriteLine("Firebase already initialized");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("Firebase initialized successfully");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Firebase options not found - skipping Firebase initialization");
                }

                // ✅ ANDROID 9 COMPATIBILITY: Safe notification channel creation
                CreateNotificationChannel();
            }
            catch (Exception ex)
            {
                // ✅ ANDROID 9 COMPATIBILITY: Don't crash app if Firebase fails to initialize
                System.Diagnostics.Debug.WriteLine($"Error during MainApplication initialization: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // App can still function without Firebase, so continue initialization
            }
        }
        /// <summary>
        /// ✅ ANDROID 9 COMPATIBILITY: Safe notification channel creation
        /// </summary>
        private void CreateNotificationChannel()
        {
            try
            {
                // ✅ ANDROID 9 COMPATIBILITY: Check API level before creating channels
                if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
                {
                    var channelId = $"{PackageName}.general";
                    var notificationManager = (NotificationManager?)GetSystemService(NotificationService);

                    if (notificationManager != null)
                    {
                        var channel = new NotificationChannel(channelId, "General", NotificationImportance.Default);
                        notificationManager.CreateNotificationChannel(channel);
                        FirebaseCloudMessagingImplementation.ChannelId = channelId;
                        System.Diagnostics.Debug.WriteLine($"Notification channel created: {channelId}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("NotificationManager not available");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Android version below API 26 - notification channels not needed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating notification channel: {ex.Message}");
                // Don't crash the app if notification channel creation fails
            }
        }

        public override void OnCreate()
        {       
            base.OnCreate();
        }

        protected override Microsoft.Maui.Hosting.MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();
    }
}
