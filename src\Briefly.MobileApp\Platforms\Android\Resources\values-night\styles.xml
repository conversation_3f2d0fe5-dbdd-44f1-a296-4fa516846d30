<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
	<!-- Base Application Theme - Dark Mode -->
	<style name="MainTheme" parent="Theme.MaterialComponents.DayNight.NoActionBar">
		<!-- Primary App Colors for Dark Mode -->
		<item name="android:colorPrimary">#666666</item>
		<item name="android:colorPrimaryDark">#444444</item>
		<item name="android:colorAccent">#555555</item>

		<!-- Enable theme-aware dark mode support -->
		<item name="android:forceDarkAllowed">true</item>

		<!-- Dark Theme Status Bar Configuration -->
		<item name="android:statusBarColor">@color/status_bar_dark</item>
		<item name="android:windowLightStatusBar">false</item>

		<!-- Required TextAppearance attributes for Material Components -->
		<item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
		<item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
		<item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
		<item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
		<item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
		<item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
		<item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
		<item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
		<item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
		<item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
		<item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
		<item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
		<item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>

		<!-- Dark Theme Navigation Bar Configuration -->
		<item name="android:navigationBarColor">@color/navigation_bar_dark</item>
		<item name="android:windowLightNavigationBar">false</item>

		<!-- Window Configuration -->
		<item name="android:windowDrawsSystemBarBackgrounds">true</item>
		<item name="android:fitsSystemWindows">false</item>
 
	</style>
</resources>
