﻿<?xml version="1.0" encoding="utf-8" ?>
<local:SignupFormViewBase
    x:Class="Platform.Client.Common.Features.Account.SignupFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Account"
    Title="Create your account"
    x:DataType="local:SignupFormView"
    Background="{StaticResource OverlayColor}"
    Shell.BackgroundColor="{AppThemeBinding Light=#********,
                                            Dark=#********}"
    Shell.TitleColor="White">

    <Grid Padding="16" Background="{AppThemeBinding Light=White, Dark={StaticResource Gray900}}">
        <ScrollView>
            <VerticalStackLayout Padding="16" Spacing="20">
                <!--  Header Section with Icon  -->
                <VerticalStackLayout Spacing="12">
                    <Border
                        BackgroundColor="{AppThemeBinding Light={StaticResource Gray200},
                                                          Dark={StaticResource Gray600}}"
                        HeightRequest="64"
                        Stroke="{AppThemeBinding Light={StaticResource Blue600},
                                                 Dark={StaticResource Blue400}}"
                        StrokeThickness="1"
                        WidthRequest="64">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="40" />
                        </Border.StrokeShape>
                        <Grid>
                            <Image
                                Aspect="AspectFill"
                                IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                Source="{Binding SelectedItem.AvatarData}" />

                            <Image
                                HorizontalOptions="Center"
                                IsVisible="{Binding SelectedItem.AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                Opacity="0.6"
                                VerticalOptions="Center">
                                <Image.Source>
                                    <FontImageSource
                                        FontFamily="Jelly"
                                        Glyph="&#xf0c0;"
                                        Size="32"
                                        Color="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />
                                </Image.Source>
                            </Image>

                        </Grid>
                    </Border>

                    <Label
                        FontAttributes="Bold"
                        FontFamily="MulishExtraBold"
                        FontSize="24"
                        HorizontalTextAlignment="Center"
                        Text="Create your account"
                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                    <Label
                        FontFamily="Poppins"
                        FontSize="14"
                        HorizontalTextAlignment="Center"
                        Opacity="0.8"
                        Text="Join us to get started with secure messaging"
                        TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                    Dark={StaticResource Gray300}}" />
                </VerticalStackLayout>

                <!--  Error Display  -->
                <Border
                    Background="{StaticResource ErrorBackgroundColor}"
                    IsVisible="{Binding HasError}"
                    Stroke="{StaticResource ErrorBorderColor}"
                    StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="Auto,*">
                        <Image
                            Grid.Column="0"
                            Margin="0,2,8,0"
                            VerticalOptions="Start">
                            <Image.Source>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf071;"
                                    Size="16"
                                    Color="{AppThemeBinding Light={StaticResource Red600},
                                                            Dark={StaticResource Red400}}" />
                            </Image.Source>
                        </Image>
                        <VerticalStackLayout Grid.Column="1" Spacing="4">
                            <Label
                                FontAttributes="Bold"
                                FontSize="14"
                                Text="Registration failed"
                                TextColor="{AppThemeBinding Light={StaticResource Red600},
                                                            Dark={StaticResource Red400}}" />
                            <Label
                                FontSize="12"
                                Text="{Binding Error}"
                                TextColor="{AppThemeBinding Light={StaticResource Red600},
                                                            Dark={StaticResource Red400}}" />
                        </VerticalStackLayout>
                    </Grid>
                </Border>

                <!--  Sign Up Form  -->
                <VerticalStackLayout x:Name="FormContainer" Spacing="16">

                    <!--  Avatar Selection  -->
                    <Picker
                        Title="Choose an avatar..."
                        FontSize="14"
                        ItemsSource="{Binding AvatarOptions}"
                        SelectedItem="{Binding SelectedItem.AvatarData}"
                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}"
                        TitleColor="{AppThemeBinding Light={StaticResource Gray400},
                                                     Dark={StaticResource Gray500}}" />
                    <!--  Display Name Field  -->
                    <VerticalStackLayout>
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="14"
                            Text="Display Name"
                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                        Dark={StaticResource Gray300}}" />
                        <Grid>
                            <Entry
                                Background="Transparent"
                                FontSize="16"
                                Placeholder="Your friends will see this name"
                                PlaceholderColor="{AppThemeBinding Light={StaticResource Gray300},
                                                                   Dark={StaticResource Gray700}}"
                                Text="{Binding SelectedItem.DisplayName}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />

                            <Image HorizontalOptions="End">
                                <Image.Source>
                                    <FontImageSource
                                        FontFamily="Jelly"
                                        Glyph="&#xf007;"
                                        Size="20"
                                        Color="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />
                                </Image.Source>
                            </Image>
                        </Grid>
                    </VerticalStackLayout>

                    <!--  Username Field  -->
                    <VerticalStackLayout>
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="14"
                            Text="Username"
                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                        Dark={StaticResource Gray300}}" />
                        <Grid>
                            <Entry
                                Background="Transparent"
                                FontSize="16"
                                Placeholder="Choose a username"
                                PlaceholderColor="{AppThemeBinding Light={StaticResource Gray300},
                                                                   Dark={StaticResource Gray700}}"
                                Text="{Binding SelectedItem.NickName}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />

                            <Image HorizontalOptions="End">
                                <Image.Source>
                                    <FontImageSource
                                        FontFamily="Jelly"
                                        Glyph="&#xf023;"
                                        Size="20"
                                        Color="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />
                                </Image.Source>
                            </Image>
                        </Grid>
                    </VerticalStackLayout>

                    <!--  Password Field  -->
                    <VerticalStackLayout Spacing="8">
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="14"
                            Text="Password"
                            TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                                        Dark={StaticResource Gray300}}" />
                        <Grid>
                            <Entry
                                Background="Transparent"
                                FontSize="16"
                                IsPassword="{Binding SelectedItem.IsPasswordHidden, FallbackValue=True}"
                                Placeholder="Create a password"
                                PlaceholderColor="{AppThemeBinding Light={StaticResource Gray300},
                                                                   Dark={StaticResource Gray700}}"
                                Text="{Binding SelectedItem.PassKey}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray300}}" />

                            <Image HorizontalOptions="End">
                                <Image.Source>
                                    <FontImageSource
                                        FontFamily="Jelly"
                                        Glyph="{Binding SelectedItem.ShowPassword, Converter={StaticResource BoolToStringConverter}, ConverterParameter='&#xf070;|&#xf06e;'}"
                                        Size="20"
                                        Color="{AppThemeBinding Light={StaticResource Gray700},
                                                                Dark={StaticResource Gray300}}" />
                                </Image.Source>
                                <Image.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding TogglePasswordVisibilityCommand}" />
                                </Image.GestureRecognizers>
                            </Image>
                        </Grid>
                    </VerticalStackLayout>

                    <!--  Enhanced Create Account Button  -->
                    <Button Command="{Binding SaveCommand}" TextColor="{AppThemeBinding Light={StaticResource Gray100}, Dark={StaticResource Gray100}}">
                         
                        <Button.Text>
                            <Binding Path="IsWorking">
                                <Binding.Converter>
                                    <StaticResource Key="BoolToStringConverter" />
                                </Binding.Converter>
                                <Binding.ConverterParameter>Creating account...|Create Account</Binding.ConverterParameter>
                            </Binding>
                        </Button.Text>
                    </Button>

                </VerticalStackLayout>
            </VerticalStackLayout>
        </ScrollView>
        <Grid
            Grid.RowSpan="3"
            Background="{StaticResource OverlayColor}"
            IsVisible="{Binding IsWorking}">

            <Border
                Padding="30"
                Background="{StaticResource LoadingBackgroundColor}"
                HorizontalOptions="Center"
                Stroke="{StaticResource Gray500Brush}"
                VerticalOptions="Center">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="16" />
                </Border.StrokeShape>
                <Border.Shadow>
                    <Shadow
                        Brush="Gray"
                        Opacity="0.2"
                        Radius="10"
                        Offset="0,4" />
                </Border.Shadow>
                <VerticalStackLayout HorizontalOptions="Center" Spacing="15">
                    <ActivityIndicator IsRunning="True" Color="{StaticResource Secondary700}" />
                    <Label
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalTextAlignment="Center"
                        Text="Working, please wait...!"
                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                </VerticalStackLayout>
            </Border>
        </Grid>
    </Grid>
</local:SignupFormViewBase>
