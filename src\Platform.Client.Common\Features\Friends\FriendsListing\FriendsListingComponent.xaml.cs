﻿using CommunityToolkit.Mvvm.Input;
using DeepMessage.Client.Common.Data;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using MobileApp.MauiShared;
using Platform.Client.Common.Features.Conversation;
using Platform.Client.Services.Features.Friends;
using Platform.Framework.Core;
using System.ComponentModel;
using System.Security.Claims;
using System.Windows.Input;

namespace Platform.Client.Common.Features.Friends;

public class FriendsListingComponentViewModel : ListingBaseMaui2<FriendsListingViewModel, FriendsListingBusinessObject,
                                        FriendsFilterViewModel, FriendsFilterBusinessObject, IFriendsListingDataService>
{


    public FriendsListingComponentViewModel(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        SyncFriendsCommand = new AsyncRelayCommand(SyncFriends, () => !IsWorking);
        ClearSearchCommand = new AsyncRelayCommand(ClearSearch);
        RefreshCommand = new AsyncRelayCommand(RefreshItems);


        PubSub.Hub.Default.Subscribe<string>(this, async (x) =>
        {
            if (x == "FriendDataUpdated")
            {
                await SyncFriends();
            }
        });
    }

    bool retry = true;
    protected override async Task ItemsLoaded(IFriendsListingDataService service)
    {
        // Auto-sync on first load if no items exist
        if (Items.Count == 0 && retry)
        {
            retry = false;
            await SyncFriends();
        }

        // Update UI state properties
        NotifyPropertyChanged(nameof(HasSearchResults));
        NotifyPropertyChanged(nameof(IsEmpty));
        NotifyPropertyChanged(nameof(TotalRecords));

        //FilterViewModel.PropertyChanged += async (sender, e) =>
        //{
        //    if (e.PropertyName == nameof(FilterViewModel.SearchText))
        //    {
        //        await LoadItems();
        //        NotifyPropertyChanged(nameof(HasSearchResults));
        //    }
        //};
    }

    #region Computed Properties for UI State Management

    /// <summary>
    /// Indicates if there's an error to display
    /// </summary>
    public bool HasError => !string.IsNullOrEmpty(Error);

    /// <summary>
    /// Indicates if there's a success message to display
    /// </summary>
    public bool HasSuccessMessage => !string.IsNullOrEmpty(SuccessMessage);

    /// <summary>
    /// Indicates if search results info should be displayed
    /// </summary>
    public bool HasSearchResults => !string.IsNullOrEmpty(FilterViewModel?.SearchText) && Items.Count > 0;

    /// <summary>
    /// Indicates if the list is empty (no items to display)
    /// </summary>
    public bool IsEmpty => Items.Count == 0 && !IsWorking;

    /// <summary>
    /// Total records count (alias for TotalRows from base class)
    /// </summary>
    public int TotalRecords => TotalRows;

    #endregion

    #region Additional Properties

    private string? _successMessage;
    /// <summary>
    /// Success message to display to user
    /// </summary>
    public string? SuccessMessage
    {
        get => _successMessage;
        set
        {
            _successMessage = value;
            NotifyPropertyChanged();
            NotifyPropertyChanged(nameof(HasSuccessMessage));
        }
    }

    private bool _isSyncing;
    /// <summary>
    /// Indicates if sync operation is in progress
    /// </summary>
    public bool IsSyncing
    {
        get => _isSyncing;
        set
        {
            _isSyncing = value;
            NotifyPropertyChanged();
        }
    }

    #endregion

    #region Override Base Properties to Trigger UI Updates

    /// <summary>
    /// Override Error property to trigger UI state updates
    /// </summary>
    public new string? Error
    {
        get => base.Error;
        set
        {
            if (base.Error != value)
            {
                base.Error = value;
                NotifyPropertyChanged();
                NotifyPropertyChanged(nameof(HasError));
            }
        }
    }

    /// <summary>
    /// Override IsWorking property to trigger UI state updates
    /// </summary>
    public new bool IsWorking
    {
        get => base.IsWorking;
        set
        {
            if (base.IsWorking != value)
            {
                base.IsWorking = value;
                NotifyPropertyChanged();
                NotifyPropertyChanged(nameof(IsEmpty));
            }
        }
    }

    #endregion


    #region Commands

    public ICommand SyncFriendsCommand { get; private set; }
    public ICommand ClearSearchCommand { get; private set; }
    public ICommand RefreshCommand { get; private set; }

    private ICommand? _startChatCommand;

    public ICommand? StartChatCommand
    {
        get { return _startChatCommand; }
        set { SetField(ref _startChatCommand, value); }
    }

    private ICommand? _addFriendCommand;

    public ICommand? AddFriendCommand
    {
        get { return _addFriendCommand; }
        set { SetField(ref _addFriendCommand, value); }
    }

    private ICommand? _editFriendCommand;

    public ICommand? EditFriendCommand
    {
        get { return _editFriendCommand; }
        set { SetField(ref _editFriendCommand, value); }
    }


    #endregion

    #region Command Implementations

    /// <summary>
    /// Synchronizes friends from server
    /// </summary>
    private async Task SyncFriends()
    {
        try
        {
            IsSyncing = true;
            Error = string.Empty;
            SuccessMessage = string.Empty;

            using var scope = ScopeFactory.CreateScope();
            var friendsClientService = scope.ServiceProvider.GetRequiredKeyedService<IFriendsListingDataService>("client");
            var pagedItems = await friendsClientService.GetPaginatedItems(new FriendsFilterBusinessObject() { RowsPerPage = 50 });

            if (pagedItems?.Items != null)
            {
                var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                foreach (var item in pagedItems.Items)
                {
                    // Check if friendship already exists
                    var friendship = await context.Friendships.FindAsync(item.Id);
                    if (friendship == null)
                    {
                        friendship = new Friendship()
                        {
                            Id = item.Id,
                            UserId = item.UserId,
                            FriendId = item.FriendId,

                        };
                        context.Friendships.Add(friendship);
                    }
                    // Update existing friendship details
                    friendship.Name = item.Name;
                    friendship.AvatarData = item.AvatarData;
                    friendship.TagLine = item.TagLine;
                    friendship.Pub1 = item.Pub1;
                }
                await context.SaveChangesAsync();
                await LoadItems();
                SuccessMessage = $"Successfully synced {pagedItems.Items.Count} friends";
            }
        }
        catch (Exception ex)
        {
            Error = ex.Message;
        }
        finally
        {
            IsSyncing = false;
        }
    }

    /// <summary>
    /// Clears the search text and refreshes the list
    /// </summary>
    private async Task ClearSearch()
    {
        try
        {
            FilterViewModel.SearchText = string.Empty;
            await LoadItems();
            NotifyPropertyChanged(nameof(HasSearchResults));
        }
        catch (Exception ex)
        {
            Error = ex.Message;
        }
    }

    /// <summary>
    /// Refreshes the friends list
    /// </summary>
    private async Task RefreshItems()
    {
        try
        {
            Error = string.Empty;
            SuccessMessage = string.Empty;
            await LoadItems();
        }
        catch (Exception ex)
        {
            Error = ex.Message;
        }
    }

    #endregion
}


public partial class FriendsListingView : ContentPage
{
    private readonly IServiceScopeFactory scopeFactory;

    public FriendsListingView(IServiceScopeFactory scopeFactory)
    {
        System.Diagnostics.Debug.WriteLine("[FRIENDS] Initializing FriendsListingView");
        this.scopeFactory = scopeFactory;

        InitializeComponent();

        var scope = scopeFactory.CreateScope();
        var viewModel = scope.ServiceProvider.GetRequiredService<FriendsListingComponentViewModel>();

        // Set up commands with proper error handling
        viewModel.AddFriendCommand = new AsyncRelayCommand(AddFriend);
        viewModel.EditFriendCommand = new AsyncRelayCommand<FriendsListingViewModel>(EditFriend);
        viewModel.StartChatCommand = new AsyncRelayCommand<FriendsListingViewModel>(StartChat);

        System.Diagnostics.Debug.WriteLine($"[FRIENDS] Commands initialized - AddFriend: {viewModel.AddFriendCommand != null}, EditFriend: {viewModel.EditFriendCommand != null}, StartChat: {viewModel.StartChatCommand != null}");

        BindingContext = viewModel;

        System.Diagnostics.Debug.WriteLine("[FRIENDS] FriendsListingView initialization complete");
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
    }

    private async Task AddFriend()
    {
        System.Diagnostics.Debug.WriteLine("[FRIENDS] Adding new friend");

        try
        {
            await Navigation.PushModalAsync(new FriendFormView(scopeFactory, string.Empty), false);
            System.Diagnostics.Debug.WriteLine("[FRIENDS] Successfully navigated to add friend form");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[FRIENDS] Error adding friend: {ex.Message}");
            // TODO: Show error message to user
            // Error = ex.Message;
        }
    }

    private async Task EditFriend(FriendsListingViewModel? friend)
    {
        if (friend == null)
        {
            System.Diagnostics.Debug.WriteLine("[FRIENDS] EditFriend called with null friend");
            return;
        }

        System.Diagnostics.Debug.WriteLine($"[FRIENDS] Editing friend: {friend.Name} (ID: {friend.Id})");

        try
        {
            await Navigation.PushModalAsync(new FriendProfileFormView(scopeFactory, friend.Id), false);
            System.Diagnostics.Debug.WriteLine($"[FRIENDS] Successfully navigated to edit friend form");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[FRIENDS] Error editing friend: {ex.Message}");
            // TODO: Show error message to user
            // Error = ex.Message;
        }
    }

    private async Task StartChat(FriendsListingViewModel? friend)
    {
        if (friend == null)
        {
            System.Diagnostics.Debug.WriteLine("[FRIENDS] StartChat called with null friend");
            return;
        }

        System.Diagnostics.Debug.WriteLine($"[FRIENDS] Starting chat with friend: {friend.Name} (ID: {friend.FriendId})");

        try
        {
            using var scope = scopeFactory.CreateScope();
            var startChatService = scope.ServiceProvider.GetRequiredService<IStartChatFormDataService>();
            var conversationId = await startChatService.SaveAsync(new StartChatFormBusinessObject() { FriendId = friend.FriendId });
            var chat = new ChatMessagesListingView(scopeFactory, conversationId, friend.Name, friend.AvatarData);
            await Navigation.PushAsync(chat, false);

        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[FRIENDS] Error starting chat: {ex.Message}");
            // Error = ex.InnerException?.Message ?? ex.Message;
        }
    }
}


