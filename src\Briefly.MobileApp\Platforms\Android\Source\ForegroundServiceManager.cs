﻿
// Platforms/Android/ForegroundServiceManager.cs
using Android.App;
using Android.Content;
using DeepMessage.MauiApp.Platforms.Android;
using DeepMessage.MauiApp.Services;

[assembly: Dependency(typeof(ForegroundServiceManager))]
namespace DeepMessage.MauiApp.Platforms.Android;

public class ForegroundServiceManager : IForegroundServiceManager
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:Validate platform compatibility", Justification = "<Pending>")]
    public void StartService()
    {
        var context = global::Android.App.Application.Context ?? throw new Exception();
        var intent = new Intent(context, typeof(DeepSyncService));
        // Starting as foreground service for API 26+
        if (!IsServiceRunning(context, typeof(DeepSyncService)))
        {
            if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.O)
                _ = context.StartForegroundService(intent);
            else
                context.StartService(intent);
        }
    }

    public void StopService()
    {
        var context = global::Android.App.Application.Context;
        var intent = new Intent(context, typeof(DeepSyncService));
        context.StopService(intent);
    }

    private bool IsServiceRunning(Context context, Type serviceClass)
    {
        var manager = context.GetSystemService(Context.ActivityService) as ActivityManager;
        if (manager == null)
        {
            throw new InvalidOperationException("ActivityManager service is not available.");
        }

        // Use RunningAppProcesses as an alternative for Android 26 or later
        if (global::Android.OS.Build.VERSION.SdkInt >= global::Android.OS.BuildVersionCodes.O)
        {
            var runningProcesses = manager.RunningAppProcesses;
            if (runningProcesses == null)
            {
                return false; // Handle null reference case
            }

            return runningProcesses.Any(process =>
                process.ProcessName == serviceClass.FullName);
        }
        else
        {
#pragma warning disable CA1422 // Validate platform compatibility
            var runningServices = manager.GetRunningServices(int.MaxValue);
#pragma warning restore CA1422 // Validate platform compatibility
            if (runningServices == null)
            {
                return false; // Handle null reference case
            }

            return runningServices.Any(service => service.Service.ClassName == serviceClass.FullName);
        }
    }
}
