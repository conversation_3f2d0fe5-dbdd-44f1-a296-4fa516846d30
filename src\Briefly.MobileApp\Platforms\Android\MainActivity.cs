﻿using Android;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Runtime;
using AndroidX.Core.App;
using AndroidX.Core.Content; 
using Plugin.Firebase.CloudMessaging; 

namespace ModelFury.Briefly.MobileApp
{
    [Activity(Theme = "@style/MainTheme", MainLauncher = true, LaunchMode = LaunchMode.SingleTop, ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation | ConfigChanges.UiMode | ConfigChanges.ScreenLayout | ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {
            base.OnCreate(savedInstanceState);

            // ✅ PERFORMANCE OPTIMIZATION: Keep only essential UI setup on main thread
            Window?.SetSoftInputMode(Android.Views.SoftInput.AdjustResize);

            // ✅ PERFORMANCE OPTIMIZATION: Defer heavy operations to background
            // This prevents blocking the UI thread during activity creation
            _ = Task.Run(async () =>
            {
                try
                {
                    // Add small delay to ensure activity is fully created
                    await Task.Delay(500);

                    await InitializeBackgroundServicesAsync();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Background initialization failed: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// ✅ PERFORMANCE OPTIMIZATION: Async background initialization
        /// Handles all heavy operations without blocking the UI thread
        /// </summary>
        private async Task InitializeBackgroundServicesAsync()
        {
            try
            {
                // Handle intent processing
                await Task.Run(() => HandleIntent(Intent));

                // Create notification channels
                await Task.Run(() => CreateNotificationChannelIfNeeded());

                // Request permissions asynchronously
                await RequestPushNotificationsPermissionAsync();

                // Initialize Firebase services
                await Task.Run(async () => await CrossFirebaseCloudMessaging.Current.CheckIfValidAsync());

                System.Diagnostics.Debug.WriteLine("Background services initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error during background initialization: {ex.Message}");
            }
        }

        private static void HandleIntent(Intent? intent)
        {
            FirebaseCloudMessagingImplementation.OnNewIntent(intent);
            //FirebaseDynamicLinksImplementation.HandleDynamicLinkAsync(intent);
        }

        /// <summary>
        /// ✅ ANDROID 9 COMPATIBILITY: Safe permission request with API level checks
        /// </summary>
        private async Task RequestPushNotificationsPermissionAsync()
        {
            try
            {
#pragma warning disable CA1416 // Validate platform compatibility
                // ✅ ANDROID 9 COMPATIBILITY: Only request POST_NOTIFICATIONS on Android 13+ (API 33+)
                if (Build.VERSION.SdkInt >= BuildVersionCodes.Tiramisu &&
                    ContextCompat.CheckSelfPermission(this, Manifest.Permission.PostNotifications) != Permission.Granted)
                {
                    await MainThread.InvokeOnMainThreadAsync(() =>
                    {
                        ActivityCompat.RequestPermissions(this, new[] { Manifest.Permission.PostNotifications }, 0);
                    });
                }
                // ✅ ANDROID 9 COMPATIBILITY: For Android 9-12, notifications work without explicit permission
                else if (Build.VERSION.SdkInt >= BuildVersionCodes.P) // Android 9+
                {
                    System.Diagnostics.Debug.WriteLine("Android 9-12 detected - notifications enabled by default");
                }
#pragma warning restore CA1416 // Validate platform compatibility
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error requesting push notification permissions: {ex.Message}");
                // Don't crash the app if permission request fails
            }
        }

        /// <summary>
        /// Legacy synchronous method - kept for compatibility
        /// </summary>
        private void RequestPushNotificationsPermission()
        {
            _ = Task.Run(async () => await RequestPushNotificationsPermissionAsync());
        }

        /// <summary>
        /// ✅ ANDROID 9 COMPATIBILITY: Safe notification channel creation
        /// </summary>
        private void CreateNotificationChannelIfNeeded()
        {
            try
            {
                // ✅ ANDROID 9 COMPATIBILITY: Android 9 (API 28) supports notification channels
                if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
                {
                    CreateNotificationChannel();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Android version below API 26 - notification channels not needed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating notification channel: {ex.Message}");
                // Don't crash the app if notification channel creation fails
            }
        }

        /// <summary>
        /// ✅ ANDROID 9 COMPATIBILITY: Safe notification channel creation with error handling
        /// </summary>
        private void CreateNotificationChannel()
        {
            try
            {
                var channelId = $"{PackageName}.general";
                var temp = GetSystemService(NotificationService);
                if (temp == null)
                {
                    System.Diagnostics.Debug.WriteLine("Notification service not available - skipping channel creation");
                    return;
                }

                var notificationManager = (NotificationManager)temp;
                var channel = new NotificationChannel(channelId, "General", NotificationImportance.Default);
                notificationManager.CreateNotificationChannel(channel);

                // ✅ ANDROID 9 COMPATIBILITY: Safe Firebase configuration
                FirebaseCloudMessagingImplementation.ChannelId = channelId;

                // ✅ ANDROID 9 COMPATIBILITY: Use a more reliable icon resource
                try
                {
                    FirebaseCloudMessagingImplementation.SmallIconRef = Resource.Drawable.mtrl_switch_thumb_pressed_unchecked;
                }
                catch
                {
                    // Fallback to default Android icon if custom icon fails
                    FirebaseCloudMessagingImplementation.SmallIconRef = Android.Resource.Drawable.IcDialogInfo;
                }

                System.Diagnostics.Debug.WriteLine($"Notification channel created successfully: {channelId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CreateNotificationChannel: {ex.Message}");
                // Don't crash the app if notification channel creation fails
            }
        }

        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, [GeneratedEnum] Permission[] grantResults)
        {
            Microsoft.Maui.ApplicationModel.Platform.OnRequestPermissionsResult(requestCode, permissions, grantResults);
            base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
        }


        protected override void OnNewIntent(Intent? intent)
        {
            base.OnNewIntent(intent);
            HandleIntent(intent);
        }
    }
}
