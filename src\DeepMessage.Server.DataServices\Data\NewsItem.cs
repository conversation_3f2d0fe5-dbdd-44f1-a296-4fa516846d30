using System.ComponentModel.DataAnnotations;

namespace DeepMessage.Server.DataServices.Data
{
    /// <summary>
    /// News article entity for caching RSS feed content
    /// Provides offline resilience and improved performance
    /// </summary>
    public class NewsItem
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        [Required, StringLength(1000)]
        public string Title { get; set; } = null!;
        
        [Required, StringLength(4000)]
        public string Description { get; set; } = null!;

        [Required, StringLength(2000)]
        public string Link { get; set; } = null!;
        
        [StringLength(2000)]
        public string? Thumbnail { get; set; }
        
        [StringLength(100)]
        public string Source { get; set; } = "BBC";
        
        public DateTime PubDate { get; set; }
        
        /// <summary>
        /// When this news item was cached in our database
        /// </summary>
        public DateTime CachedAt { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Whether this news item is active/visible
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// Additional metadata stored as JSON
        /// </summary>
        public string? JsonData { get; set; }
    }
}
