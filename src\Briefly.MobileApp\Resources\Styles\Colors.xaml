﻿<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-comp compile="true" ?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/dotnet/2021/maui" xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <!--  Note: For Android please see also Platforms\Android\Resources\values\colors.xml 50-F3F4F6, 200-D1D5DB  -->

    <Color x:Key="Primary">#404040</Color>
    <Color x:Key="Secondary">#202020</Color>

    <Color x:Key="White">White</Color>
    <Color x:Key="Black">Black</Color>
    <Color x:Key="Magenta">#6E6E6E</Color>
    <Color x:Key="MidnightBlue">#190649</Color>
    <Color x:Key="OffBlack">#1f1f1f</Color>

    <Color x:Key="Gray20">#FAFAFA</Color>
    <Color x:Key="Gray50">#F4F4F5</Color>
    <Color x:Key="Gray100">#E1E1E1</Color>
    <Color x:Key="Gray200">#C8C8C8</Color>
    <Color x:Key="Gray300">#ACACAC</Color>
    <Color x:Key="Gray400">#919191</Color>
    <Color x:Key="Gray500">#6E6E6E</Color>
    <Color x:Key="Gray600">#404040</Color>
    <Color x:Key="Gray700">#303030</Color>
    <Color x:Key="Gray800">#2A2A2A</Color>
    <Color x:Key="Gray900">#202020</Color>
    <Color x:Key="Gray950">#141414</Color>

    <Color x:Key="Red50">#fef2f2</Color>
    <Color x:Key="Red100">#fee2e2</Color>
    <Color x:Key="Red200">#fecaca</Color>
    <Color x:Key="Red300">#fca5a5</Color>
    <Color x:Key="Red400">#f87171</Color>
    <Color x:Key="Red500">#ef4444</Color>
    <Color x:Key="Red600">#dc2626</Color>
    <Color x:Key="Red700">#b91c1c</Color>
    <Color x:Key="Red800">#991b1b</Color>
    <Color x:Key="Red900">#7f1d1d</Color>
    <Color x:Key="Red950">#450a0a</Color>

 

    <Color x:Key="Secondary50">#f6f7ef</Color>
    <Color x:Key="Secondary100">#e8ebd6</Color>
    <Color x:Key="Secondary200">#d4d9af</Color>
    <Color x:Key="Secondary300">#c4c88d</Color>
    <Color x:Key="Secondary400">#aeb05f</Color>
    <Color x:Key="Secondary500">#a1a051</Color>
    <Color x:Key="Secondary600">#8a8344</Color>
    <Color x:Key="Secondary700">#6f6539</Color>
    <Color x:Key="Secondary800">#5f5534</Color>
    <Color x:Key="Secondary900">#524831</Color>
    <Color x:Key="Secondary950">#2f2819</Color>

    <Color x:Key="BostonBlue50">#f2f8f9</Color>
    <Color x:Key="BostonBlue100">#ddecf0</Color>
    <Color x:Key="BostonBlue200">#bfdae2</Color>
    <Color x:Key="BostonBlue300">#93c0cd</Color>
    <Color x:Key="BostonBlue400">#5f9eb1</Color>
    <Color x:Key="BostonBlue500">#498ca2</Color>
    <Color x:Key="BostonBlue600">#3b6b7f</Color>
    <Color x:Key="BostonBlue700">#355969</Color>
    <Color x:Key="BostonBlue800">#324b58</Color>
    <Color x:Key="BostonBlue900">#2d414c</Color>
    <Color x:Key="BostonBlue950">#1a2932</Color>

    <Color x:Key="Blue600">#2563eb</Color>
    <Color x:Key="Blue400">#4583ff</Color>
    
    <SolidColorBrush x:Key="TitleBarColor" Color="{StaticResource Primary}" />
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource Primary}" />
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource Secondary}" />
    <SolidColorBrush x:Key="WhiteBrush" Color="{StaticResource White}" />
    <SolidColorBrush x:Key="BlackBrush" Color="{StaticResource Black}" />

    <SolidColorBrush x:Key="Gray100Brush" Color="{StaticResource Gray100}" />
    <SolidColorBrush x:Key="Gray200Brush" Color="{StaticResource Gray200}" />
    <SolidColorBrush x:Key="Gray300Brush" Color="{StaticResource Gray300}" />
    <SolidColorBrush x:Key="Gray400Brush" Color="{StaticResource Gray400}" />
    <SolidColorBrush x:Key="Gray500Brush" Color="{StaticResource Gray500}" />
    <SolidColorBrush x:Key="Gray600Brush" Color="{StaticResource Gray600}" />
    <SolidColorBrush x:Key="Gray900Brush" Color="{StaticResource Gray900}" />
    <SolidColorBrush x:Key="Gray950Brush" Color="{StaticResource Gray950}" />

    <!--  Theme-aware colors for UI components  -->
    <SolidColorBrush x:Key="PageBackgroundColor" Color="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}" />
    <SolidColorBrush x:Key="CardBackgroundColor" Color="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray900}}" />
    <SolidColorBrush x:Key="SurfaceColor" Color="{AppThemeBinding Light={StaticResource Gray50}, Dark={StaticResource Gray700}}" />
    <SolidColorBrush x:Key="BorderColor" Color="{AppThemeBinding Light={StaticResource Gray200}, Dark={StaticResource Gray600}}" />
    <SolidColorBrush x:Key="TextPrimaryColor" Color="{AppThemeBinding Light={StaticResource Gray700}, Dark={StaticResource White}}" />
    <SolidColorBrush x:Key="TextSecondaryColor" Color="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray300}}" />
    <SolidColorBrush x:Key="TextTertiaryColor" Color="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray400}}" />
    <SolidColorBrush x:Key="PlaceholderColor" Color="{AppThemeBinding Light=#9CA3AF, Dark=#6B7280}" />
    <SolidColorBrush x:Key="ErrorBackgroundColor" Color="{AppThemeBinding Light=#FEF2F2, Dark=#7F1D1D}" />
    <SolidColorBrush x:Key="ErrorBorderColor" Color="{AppThemeBinding Light=#FECACA, Dark=#991B1B}" />
    <SolidColorBrush x:Key="ErrorTextColor" Color="{AppThemeBinding Light=#DC2626, Dark=#FCA5A5}" />
    <SolidColorBrush x:Key="OverlayColor" Color="{AppThemeBinding Light=#50000000, Dark=#80000000}" />
    <SolidColorBrush x:Key="LoadingBackgroundColor" Color="{AppThemeBinding Light={StaticResource White}, Dark={StaticResource Gray800}}" />
</ResourceDictionary>