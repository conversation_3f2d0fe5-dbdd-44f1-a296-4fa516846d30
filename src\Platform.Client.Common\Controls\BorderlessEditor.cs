using AndroidX.AppCompat.Widget;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Handlers;
using Microsoft.Maui.Platform;
using Microsoft.Maui.Controls.Platform;

#if ANDROID
using AndroidX.Core.Content;
using Android.Content.Res;
#endif

namespace Platform.Client.Common.Controls
{
    /// <summary>
    /// Custom Editor control that removes the default platform underline/border
    /// </summary>
    public class BorderlessEditor : Editor
    {
        public BorderlessEditor()
        {
            BackgroundColor = Colors.Transparent;
        }
    }

    /// <summary>
    /// Custom handler for BorderlessEditor to remove platform-specific underlines
    /// Compatible with .NET 10 Material Components requirements
    /// </summary>
    public class BorderlessEditorHandler : EditorHandler
    {
        public BorderlessEditorHandler() : base()
        {
        }

        protected override void ConnectHandler(MauiAppCompatEditText platformView)
        {
            base.ConnectHandler(platformView);

#if ANDROID
            // Remove underline on Android - .NET 10 compatible approach
            if (platformView != null)
            {
                try
                {
                    // Use transparent drawable instead of null to maintain Material Components compatibility
                    var transparentDrawable = AndroidX.Core.Content.ContextCompat.GetDrawable(
                        platformView.Context,
                        Resource.Drawable.m3_tabs_transparent_background);

                    platformView.SetBackground(transparentDrawable);

                    // Set transparent background tint
                    platformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(
                        Android.Graphics.Color.Transparent);

                    // Ensure proper text appearance is maintained for Material Components
                    platformView.SetTextAppearance(Android.Resource.Style.TextAppearanceMaterialBody1);
                }
                catch (System.Exception ex)
                {
                    // Fallback to original approach if resources are not available
                    System.Diagnostics.Debug.WriteLine($"BorderlessEditor fallback: {ex.Message}");
                    platformView.SetBackgroundColor(Android.Graphics.Color.Transparent);
                    platformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(
                        Android.Graphics.Color.Transparent);
                }
            }
#elif IOS
            // Remove border on iOS
            if (platformView != null)
            {
                platformView.Layer.BorderWidth = 0;
                platformView.BackgroundColor = UIKit.UIColor.Clear;
            }
#elif WINDOWS
            // Remove border on Windows
            if (platformView != null)
            {
                platformView.BorderThickness = new Microsoft.UI.Xaml.Thickness(0);
                platformView.Background = null;
            }
#endif
        }

    }

    /// <summary>
    /// Custom Entry control that removes the default platform underline/border
    /// </summary>
    public class BorderlessEntry : Entry
    {
        public BorderlessEntry()
        {
            BackgroundColor = Colors.Transparent;
        }
    }

    /// <summary>
    /// Custom handler for BorderlessEntry to remove platform-specific underlines
    /// Compatible with .NET 10 Material Components requirements
    /// </summary>
    public class BorderlessEntryHandler : EntryHandler
    {
        public BorderlessEntryHandler() : base()
        {
        }

        protected override void ConnectHandler(MauiAppCompatEditText platformView)
        {
            base.ConnectHandler(platformView);

#if ANDROID
            // Remove underline on Android - .NET 10 compatible approach
            if (platformView != null)
            {
                try
                {
                    // Use transparent drawable instead of null to maintain Material Components compatibility
                    var transparentDrawable = AndroidX.Core.Content.ContextCompat.GetDrawable(
                        platformView.Context,
                        Resource.Drawable.m3_tabs_transparent_background);

                    platformView.SetBackground(transparentDrawable);

                    // Set transparent background tint
                    PlatformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(
                        Android.Graphics.Color.Transparent);

                    // Ensure proper text appearance is maintained for Material Components
                    platformView.SetTextAppearance(Resource.Style.TextAppearance_MaterialComponents_Body1);
                }
                catch (System.Exception ex)
                {
                    // Fallback to original approach if resources are not available
                    System.Diagnostics.Debug.WriteLine($"BorderlessEntry fallback: {ex.Message}");
                    platformView.SetBackgroundColor(Android.Graphics.Color.Transparent);
                    PlatformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(
                        Android.Graphics.Color.Transparent);
                }
            }
#elif IOS
            // Remove border on iOS
            if (platformView != null)
            {
                platformView.Layer.BorderWidth = 0;
                platformView.BackgroundColor = UIKit.UIColor.Clear;
            }
#elif WINDOWS
            // Remove border on Windows
            if (platformView != null)
            {
                platformView.BorderThickness = new Microsoft.UI.Xaml.Thickness(0);
                platformView.Background = null;
            }
#endif
        }
    }
}
