﻿using DeepMessage.ServiceContracts.Enums;

namespace Platform.Client.Services.Features.Conversation;
public class ChatThreadsListingViewModel
{
    public string Id { get; set; } = null!;

    public string? Avatar { get; set; }

    public string? Name { get; set; }

    public string? LastMessage { get; set; }

    public DateTime? LastMessageTime { get; set; }

    public string? LastMessageTimeString
    {
        get
        {
            var today = DateTime.UtcNow.Date;

            if (LastMessageTime == null)
                return "Never";
            if (LastMessageTime?.Date == today)
                return LastMessageTime?.ToString("hh:mm tt");
            else if (LastMessageTime?.Date == today.AddDays(-1))
                return "Yesterday";
            else if (LastMessageTime?.Year == 1)
                return "Never";
            else
                return LastMessageTime?.ToString("dd/MM/yyyy");
        }
    }

    public MessageDeliveryStatus? DeliveryStatus { get; set; }

    public bool? IsIncoming { get; set; }

    public string? DeliverySymbol
    {
        get
        {
            if (IsIncoming.GetValueOrDefault())
                return null;

            switch (DeliveryStatus)
            {
                case MessageDeliveryStatus.Pending:
                    return "";

                case MessageDeliveryStatus.SentToMessageServer:
                case MessageDeliveryStatus.SentToEndUserViaSignalR:
                case MessageDeliveryStatus.SentToEndUserViaPushNotification:
                    return "sent.svg";

                case MessageDeliveryStatus.DeliveredToEndUser:
                    return "delivered.svg";

                case MessageDeliveryStatus.ReadByEndUser:
                    return "read.svg";
                default:
                    return null;
            }
        }
    }
}
