﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Platform.Client.Common.Features.Friends.FriendsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:Platform.Client.Common.Controls"
    xmlns:friends="clr-namespace:Platform.Client.Services.Features.Friends;assembly=Platform.Client.Services"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    Title="Friends"
    x:DataType="local:FriendsListingComponentViewModel"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.TitleColor="{AppThemeBinding Light={StaticResource Gray800},
                                       Dark={StaticResource Gray50}}">
    <ContentPage.ToolbarItems>
        <ToolbarItem Command="{Binding AddFriendCommand}">
            <ToolbarItem.IconImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf055;"
                    Size="20"
                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            </ToolbarItem.IconImageSource>
        </ToolbarItem>
        <ToolbarItem Command="{Binding SyncFriendsCommand}">
            <ToolbarItem.IconImageSource>
                <FontImageSource
                    FontFamily="Jelly"
                    Glyph="&#xf021;"
                    Size="16"
                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            </ToolbarItem.IconImageSource>
        </ToolbarItem>
    </ContentPage.ToolbarItems>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="8"/>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>

        <!--  Search Bar  -->
        <Border
            Margin="8,0,8,0"
            Background="{AppThemeBinding Light={StaticResource Gray50},
                                         Dark={StaticResource Gray800}}"
            Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                     Dark={StaticResource Gray500}}">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="45" />
            </Border.StrokeShape>
            <Grid>
                <Image
                    Margin="16,0"
                    HorizontalOptions="End"
                    MaximumHeightRequest="32">
                    <Image.Source>
                        <FontImageSource
                            FontFamily="Jelly"
                            Glyph="&#xf002;"
                            Size="20"
                            Color="{AppThemeBinding Light={StaticResource Gray700},
                                                    Dark={StaticResource Gray300}}" />
                    </Image.Source>
                </Image>
                <controls:BorderlessEntry
                    Grid.Column="0"
                    Margin="12,0"
                    FontSize="16"
                    HorizontalOptions="Fill"
                    Placeholder="Search Friends..."
                    Text="{Binding FilterViewModel.SearchText}" />


            </Grid>
        </Border>

        <!--  Friends List  -->
        <CollectionView
            x:Name="FriendsCollectionView"
            Grid.Row="2"
            Background="{AppThemeBinding Light=White,
                                         Dark={StaticResource Gray800}}"
            IsVisible="{Binding IsEmpty, Converter={StaticResource InverseBoolConverter}}"
            ItemsSource="{Binding Items}"
            VerticalOptions="Fill">
            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="friends:FriendsListingViewModel">
                    <Grid Padding="16,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="32"/>
                            <ColumnDefinition Width="0"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingComponentViewModel}}, Path=StartChatCommand}" CommandParameter="{Binding .}" />
                        </Grid.GestureRecognizers>

                        <!--  Avatar with Status  -->
                        <Grid HeightRequest="48" WidthRequest="48">
                            <Border
                                BackgroundColor="{StaticResource Gray200}"
                                HeightRequest="48"
                                Stroke="{StaticResource Gray400}"
                                WidthRequest="48">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="24" />
                                </Border.StrokeShape>
                                <Grid>
                                    <Image
                                        Aspect="AspectFill"
                                        IsVisible="{Binding AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                        Source="{Binding AvatarData}" />
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="18"
                                        HorizontalOptions="Center"
                                        IsVisible="{Binding AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                        Text="{Binding Name, Converter={StaticResource InitialsConverter}}"
                                        TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                                    Dark={StaticResource Gray200}}"
                                        VerticalOptions="Center" />
                                </Grid>
                            </Border>
                            <!--  Online Status  -->
                            <!--<Ellipse
                                Margin="0,0,-2,-2"
                                Fill="#10B981"
                                HeightRequest="14"
                                HorizontalOptions="End"
                                VerticalOptions="End"
                                WidthRequest="14" />-->
                        </Grid>

                        <!--  Friend Info  -->
                        <VerticalStackLayout
                            Grid.Column="1"
                            Margin="12,0,8,0"
                            Spacing="2"
                            VerticalOptions="Center">
                            <Label
                                FontAttributes="Bold"
                                FontSize="16"
                                LineBreakMode="TailTruncation"
                                Text="{Binding Name}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray800},
                                                            Dark={StaticResource Gray100}}" />
                            <Label
                                FontSize="14"
                                IsVisible="{Binding TagLine, Converter={StaticResource StringToBoolConverter}}"
                                LineBreakMode="TailTruncation"
                                Text="{Binding TagLine}"
                                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray200}}" />
                        </VerticalStackLayout>

                        <!--  Edit Button  -->
                        <Button
                            Grid.Column="2"
                            BackgroundColor="Transparent"
                            Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingComponentViewModel}}, Path=StartChatCommand}"
                            CommandParameter="{Binding .}"
                            CornerRadius="0">
                            <Button.ImageSource>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf075;"
                                    Size="18"
                                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray200}}" />
                            </Button.ImageSource>
                        </Button>

                        <!--  Chat Button  -->
                        <Button
                            Grid.Column="4"
                            WidthRequest="52"
                            BackgroundColor="Transparent"
                            Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingComponentViewModel}}, Path=EditFriendCommand}"
                            CommandParameter="{Binding .}"
                            CornerRadius="0">
                            <Button.ImageSource>
                                <FontImageSource
                                    FontFamily="Jelly"
                                    Glyph="&#xf303;"
                                    Size="18"
                                    Color="{AppThemeBinding Light={StaticResource Gray700},
                                                            Dark={StaticResource Gray200}}" />
                            </Button.ImageSource>
                        </Button>
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <!--  Error Message  -->
        <Border
            Grid.Row="2"
            Margin="16,0,16,8"
            BackgroundColor="{AppThemeBinding Light={StaticResource Red50},
                                              Dark={StaticResource Red600}}"
            IsVisible="{Binding HasError}"
            Stroke="{AppThemeBinding Light={StaticResource Red400},
                                     Dark={StaticResource Red600}}"
            StrokeThickness="1">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="8" />
            </Border.StrokeShape>
            <VerticalStackLayout Padding="12" Spacing="8">
                <Grid ColumnDefinitions="Auto,*">
                    <Image
                        Grid.Column="0"
                        Margin="0,2,8,0"
                        VerticalOptions="Start">
                        <Image.Source>
                            <FontImageSource
                                FontFamily="Jelly"
                                Glyph="&#xf071;"
                                Size="16"
                                Color="{AppThemeBinding Light={StaticResource Red700},
                                                        Dark={StaticResource Red200}}" />
                        </Image.Source>
                    </Image>
                    <VerticalStackLayout Grid.Column="1" Spacing="4">
                        <Label
                            FontAttributes="Bold"
                            FontSize="14"
                            Text="Error loading friends"
                            TextColor="{AppThemeBinding Light={StaticResource Red700},
                                                        Dark={StaticResource Red200}}" />
                        <Label
                            FontSize="14"
                            Text="{Binding Error}"
                            TextColor="{AppThemeBinding Light={StaticResource Red700},
                                                        Dark={StaticResource Red200}}" />
                    </VerticalStackLayout>
                </Grid>
            </VerticalStackLayout>
        </Border>

        <!--  Search Results Info  -->
        <Border
            Grid.Row="2"
            Margin="16,0,16,8"
            BackgroundColor="{AppThemeBinding Light={StaticResource BostonBlue50},
                                              Dark={StaticResource BostonBlue900}}"
            IsVisible="{Binding HasSearchResults, Mode=OneWay}"
            Stroke="{AppThemeBinding Light={StaticResource BostonBlue200},
                                     Dark={StaticResource BostonBlue700}}"
            StrokeThickness="1">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="8" />
            </Border.StrokeShape>
            <Grid Padding="12" ColumnDefinitions="*,Auto">
                <Label
                    Grid.Column="0"
                    FontSize="14"
                    TextColor="{AppThemeBinding Light={StaticResource BostonBlue700},
                                                Dark={StaticResource BostonBlue100}}">
                    <Label.FormattedText>
                        <FormattedString>
                            <Span FontAttributes="Bold" Text="{Binding TotalRecords}" />
                            <Span Text=" friends found for " />
                            <Span FontAttributes="Bold" Text="{Binding FilterViewModel.SearchText, StringFormat='&quot;{0}&quot;'}" />
                        </FormattedString>
                    </Label.FormattedText>
                </Label>
                <Button
                    Grid.Column="1"
                    Padding="8,4"
                    BackgroundColor="Transparent"
                    Command="{Binding ClearSearchCommand, Mode=OneWay}"
                    FontSize="14"
                    Text="Clear search"
                    TextColor="{AppThemeBinding Light={StaticResource BostonBlue700},
                                                Dark={StaticResource BostonBlue200}}" />
            </Grid>
        </Border>

        <!--  Empty State  -->
        <VerticalStackLayout
            Grid.Row="2"
            Padding="16,64,16,16"
            HorizontalOptions="Center"
            IsVisible="{Binding IsEmpty, Mode=OneWay}"
            Spacing="16">
            <Image HorizontalOptions="Center">
                <Image.Source>
                    <FontImageSource
                        FontFamily="Jelly"
                        Glyph="&#xf0c0;"
                        Size="64"
                        Color="{AppThemeBinding Light={StaticResource Gray400},
                                                Dark={StaticResource Gray500}}" />
                </Image.Source>
            </Image>
            <Label
                FontAttributes="Bold"
                FontSize="18"
                HorizontalTextAlignment="Center"
                Text="No friends found"
                TextColor="{AppThemeBinding Light={StaticResource Gray700},
                                            Dark={StaticResource Gray300}}" />
            <Label
                FontSize="14"
                HorizontalTextAlignment="Center"
                MaximumWidthRequest="320"
                TextColor="{AppThemeBinding Light={StaticResource Gray500},
                                            Dark={StaticResource Gray400}}">
                <Label.Text>
                    <Binding Path="FilterViewModel.SearchText">
                        <Binding.Converter>
                            <StaticResource Key="StringToBoolConverter" />
                        </Binding.Converter>
                        <Binding.ConverterParameter>No friends match your search|You haven't added any friends yet. Start by adding your first friend!</Binding.ConverterParameter>
                    </Binding>
                </Label.Text>
            </Label>
            <!--  Clear Search Button (visible when searching)  -->
            <Button
                Padding="24,12"
                BackgroundColor="{AppThemeBinding Light={StaticResource BostonBlue600},
                                                  Dark={StaticResource BostonBlue500}}"
                Command="{Binding ClearSearchCommand}"
                FontAttributes="Bold"
                FontSize="14"
                HeightRequest="44"
                IsVisible="{Binding FilterViewModel.SearchText, Converter={StaticResource StringToBoolConverter}}"
                Text="Clear Search"
                TextColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource White}}" />

            <!--  Add Friend Button (visible when not searching)  -->
            <Button
                Padding="24,12"
                BackgroundColor="{AppThemeBinding Light={StaticResource BostonBlue600},
                                                  Dark={StaticResource BostonBlue500}}"
                Command="{Binding AddFriendCommand}"
                FontAttributes="Bold"
                FontSize="14"
                HeightRequest="44"
                IsVisible="{Binding FilterViewModel.SearchText, Converter={StaticResource InverseStringToBoolConverter}}"
                Text="Add Your First Friend"
                TextColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource White}}" />
        </VerticalStackLayout>


        <Border
            Grid.Row="2"
            Stroke="{AppThemeBinding Light={StaticResource Gray100},
                                     Dark={StaticResource Gray800}}"
            StrokeThickness="0.5"
            VerticalOptions="End" />
    </Grid>
</ContentPage>
