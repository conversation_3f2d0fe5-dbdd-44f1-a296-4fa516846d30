using DeepMessage.Server.DataServices.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Xml.Linq;

namespace DeepMessage.Server.DataServices.Services
{
    /// <summary>
    /// Background service that periodically refreshes the news cache
    /// Ensures offline resilience and improved performance
    /// </summary>
    public class NewsCacheService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<NewsCacheService> _logger;
        private readonly TimeSpan _refreshInterval = TimeSpan.FromMinutes(30); // Refresh every 30 minutes
        private const string FeedUrl = "https://feeds.bbci.co.uk/news/rss.xml";
        private static readonly XNamespace MediaNamespace = "http://search.yahoo.com/mrss/";

        public NewsCacheService(IServiceProvider serviceProvider, ILogger<NewsCacheService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("News Cache Service started");

            // Initial refresh on startup
            await RefreshNewsCache();

            // Periodic refresh
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(_refreshInterval, stoppingToken);
                    await RefreshNewsCache();
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in news cache refresh cycle");
                }
            }

            _logger.LogInformation("News Cache Service stopped");
        }

        /// <summary>
        /// Refreshes the news cache by fetching from external RSS feed
        /// </summary>
        public async Task RefreshNewsCache()
        {
            try
            {
                _logger.LogDebug("Starting news cache refresh");

                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                // Fetch news from external RSS feed
                var newsItems = await FetchNewsFromRss();
                
                if (newsItems.Any())
                {
                    var savedCount = 0;
                    
                    foreach (var newsItem in newsItems)
                    {
                        // Check if news item already exists (by link)
                        var existingItem = await context.NewsItems
                            .FirstOrDefaultAsync(n => n.Link == newsItem.Link);

                        if (existingItem == null)
                        {
                            // Add new news item
                            newsItem.Id = Guid.CreateVersion7().ToString();
                            newsItem.CachedAt = DateTime.UtcNow;
                            context.NewsItems.Add(newsItem);
                            savedCount++;
                        }
                        else
                        {
                            // Update existing item if needed
                            existingItem.Title = newsItem.Title;
                            existingItem.Description = newsItem.Description;
                            existingItem.Thumbnail = newsItem.Thumbnail;
                            existingItem.PubDate = newsItem.PubDate;
                            existingItem.CachedAt = DateTime.UtcNow;
                        }
                    }

                    await context.SaveChangesAsync();
                    
                    // Clean up old news items (older than 7 days)
                    var cutoffDate = DateTime.UtcNow.AddDays(-7);
                    var oldItems = await context.NewsItems
                        .Where(n => n.CachedAt < cutoffDate)
                        .ToListAsync();
                    
                    if (oldItems.Any())
                    {
                        context.NewsItems.RemoveRange(oldItems);
                        await context.SaveChangesAsync();
                        _logger.LogDebug("Cleaned up {Count} old news items", oldItems.Count);
                    }

                    _logger.LogInformation("News cache refreshed successfully. Added {NewCount} new items", savedCount);
                }
                else
                {
                    _logger.LogWarning("No news items fetched from RSS feed");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh news cache");
            }
        }

        /// <summary>
        /// Fetches news items from the external RSS feed
        /// </summary>
        private async Task<List<NewsItem>> FetchNewsFromRss()
        {
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(30);
                
                var response = await client.GetStringAsync(FeedUrl);
                var doc = XDocument.Parse(response);

                var items = doc.Descendants("item").Select(item => new NewsItem
                {
                    Title = item.Element("title")?.Value ?? "No Title",
                    Description = item.Element("description")?.Value ?? "No Description",
                    Link = item.Element("link")?.Value ?? "",
                    Thumbnail = item.Element(MediaNamespace + "thumbnail")?.Attribute("url")?.Value,
                    PubDate = DateTime.TryParse(item.Element("pubDate")?.Value, out var date) ? date : DateTime.UtcNow,
                    Source = "BBC",
                    IsActive = true
                }).Where(item => !string.IsNullOrEmpty(item.Link)).ToList();

                return items;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to fetch news from RSS feed: {FeedUrl}", FeedUrl);
                return new List<NewsItem>();
            }
        }

        /// <summary>
        /// Gets a random news headline for push notifications
        /// </summary>
        public async Task<string> GetRandomNewsHeadlineAsync()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                // Get recent news items (last 24 hours)
                var recentCutoff = DateTime.UtcNow.AddHours(-24);
                var recentNews = await context.NewsItems
                    .Where(n => n.IsActive && n.PubDate >= recentCutoff)
                    .OrderByDescending(n => n.PubDate)
                    .Take(10)
                    .ToListAsync();

                if (recentNews.Any())
                {
                    // Return a random headline from recent news
                    var random = new Random();
                    var randomNews = recentNews[random.Next(recentNews.Count)];
                    return randomNews.Title;
                }

                // Fallback to any cached news if no recent news
                var anyNews = await context.NewsItems
                    .Where(n => n.IsActive)
                    .OrderByDescending(n => n.PubDate)
                    .Take(5)
                    .ToListAsync();

                if (anyNews.Any())
                {
                    var random = new Random();
                    var randomNews = anyNews[random.Next(anyNews.Count)];
                    return randomNews.Title;
                }

                // Ultimate fallback
                return "Breaking News Update";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get random news headline");
                return "News Update";
            }
        }
    }
}
